import 'dart:io';

import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/exceptions.dart';
import '../../domain/repository/broker_register_repository.dart';

class BrokerRegisterRepositoryImpl extends BrokerRegisterRepository {
  BrokerRegisterRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String brokerRegUrl = APIConfig.brokerRegistration;
  static const String fileUploadUrl = APIConfig.agentFileUpload;

  @override
  Future<String?> registerBroker(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(brokerRegUrl, data: payload);
      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData != null &&
            responseData['data'] != null &&
            responseData['data']['userID'] != null) {
          return responseData['data']['userID'].toString();
        }
        return null;
      }
      if (response.statusCode == 401) {
      } else {}
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      if (statusCode == 401) {
      } else {}
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<void> uploadFile({
    required String userId,
    required String categoryType,
    required String documentType,
    required PlatformFile file,
  }) async {
    try {
      final dio = await DioClient.getDio();
      MultipartFile multipartFile;
      if (kIsWeb) {
        final platformFile = file;
        multipartFile = MultipartFile.fromBytes(
          platformFile.bytes!,
          filename: platformFile.name,
        );
      } else {
        final ioFile = file;
        if (ioFile.path == null) {
          throw ApiException(message: filePathNull, statusCode: 400);
        }
        multipartFile = await MultipartFile.fromFile(
          ioFile.path ?? '',
          filename: ioFile.path?.split('/').last,
        );
      }
      FormData formData = FormData.fromMap({
        'userId': userId,
        'categoryType': categoryType,
        'documentType': documentType,
        'file': multipartFile,
      });
      final response = await dio.post(
        fileUploadUrl,
        data: formData,
        options: Options(contentType: 'multipart/form-data'),
      );

      if (response.statusCode == 200) {
        return;
      }

      if (response.statusCode == 401) {
      } else {
        throw ApiException(
          message: response.data['message'] ?? fileUploadFailed,
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      if (statusCode == 401) {
      } else {
        throw ApiException(
          message: e.response?.data['message'] ?? fileUploadFailed,
          statusCode: statusCode ?? 500,
        );
      }
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<void> registerBrokerWithFiles({
    required Map<String, dynamic> brokerPayload,
    required List<FileUploadData> files,
    required String userId,
  }) async {
    // Upload each file sequentially
    for (final fileData in files) {
      await uploadFile(
        userId: userId,
        categoryType: fileData.categoryType,
        documentType: fileData.documentType,
        file: fileData.file,
      );
    }
  }
}

class UnauthorizedException {}
