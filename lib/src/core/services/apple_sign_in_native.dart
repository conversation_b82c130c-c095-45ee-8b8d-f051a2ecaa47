import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'apple_sign_in_service.dart';

class AppleSignInServiceNative implements AppleSignInService {
  @override
  Future<bool> isAvailable() async {
    try {
      return await SignInWithApple.isAvailable();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<AppleSignInResult?> signIn() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      return AppleSignInResult(
        userIdentifier: credential.userIdentifier ?? '',
        email: credential.email,
        givenName: credential.givenName,
        familyName: credential.familyName,
      );
    } catch (e) {
      rethrow;
    }
  }
}

AppleSignInService getAppleSignInService() => AppleSignInServiceNative();
